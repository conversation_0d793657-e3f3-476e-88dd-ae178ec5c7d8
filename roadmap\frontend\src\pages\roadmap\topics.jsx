import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import MdxViewer from "./mdxViewer";

const Topics = () => {
  const id = useParams().id;
  const day = useParams().day;
  const topic = useParams().topic || 1;

  const [topicData, setTopicData] = React.useState(null);

  useEffect(() => {
    const fetchRoadmap = async () => {
      try {
        const response = await fetch(
          `http://127.0.0.1:8000/roadmap/${id}/day/${day}`
        );
        const data = await response.json();
        if (data.success) {
          const filteredData = data.data.filter((item) => item.topic_number == topic)[0];
          setTopicData(filteredData);
          console.log(data.data);
          console.log(filteredData);
        }
      } catch (error) {
        console.error(error);
      }
    };
    fetchRoadmap();
  }, [id, day, topic]);
  return (
    <div className="w-screen min-h-screen px-16 py-10 ">
        {topicData ? (
      <div>
        <h1 className="text-white text-2xl text-start font-semibold">
          {topicData.title}
        </h1>
        <p>{topicData.description}</p>
        <div>
          <MdxViewer source={topicData.content} />
          <p>{topicData.content}</p>
        </div>
      </div>):(<div>Loading...</div>)}
    </div>
  );
};

export default Topics;
