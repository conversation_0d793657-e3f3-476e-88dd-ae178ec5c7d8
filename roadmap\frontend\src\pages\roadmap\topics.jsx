import React, { useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import Mdx<PERSON><PERSON><PERSON> from "./mdxViewer";
import { ArrowLeft, Clock, BookOpen } from "lucide-react";

const Topics = () => {
  const { id, day, topic = 1 } = useParams();
  const navigate = useNavigate();

  const [topicData, setTopicData] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  useEffect(() => {
    const fetchRoadmap = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(
          `http://127.0.0.1:8000/api/v1/roadmap/${id}/day/${day}`
        );
        const data = await response.json();
        if (data.success) {
          const filteredData = data.data.filter((item) => item.topic_number == topic)[0];
          setTopicData(filteredData);
          console.log(data.data);
          console.log(filteredData);
        } else {
          setError("Failed to load topic data");
        }
      } catch (err) {
        console.error(err);
        setError("Failed to fetch topic data");
      } finally {
        setLoading(false);
      }
    };
    fetchRoadmap();
  }, [id, day, topic]);
  if (loading) {
    return (
      <div className="w-screen min-h-screen px-4 md:px-16 py-10 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-screen min-h-screen px-4 md:px-16 py-10 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <div className="text-red-600 dark:text-red-400 text-xl mb-4">
              {error}
            </div>
            <button
              onClick={() => navigate(-1)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!topicData) {
    return (
      <div className="w-screen min-h-screen px-4 md:px-16 py-10 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <div className="text-gray-600 dark:text-gray-400 text-xl mb-4">
              Topic not found
            </div>
            <button
              onClick={() => navigate(-1)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-screen min-h-screen px-4 md:px-16 py-10 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto">
        {/* Navigation */}
        <div className="mb-8">
          <button
            onClick={() => navigate(`/roadmap/${id}/day/${day}`)}
            className="flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors mb-4"
          >
            <ArrowLeft size={20} />
            Back to Day {day}
          </button>

          {/* Breadcrumb */}
          <nav className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            <span>Roadmap</span>
            <span className="mx-2">›</span>
            <span>Day {day}</span>
            <span className="mx-2">›</span>
            <span className="text-gray-900 dark:text-white">Topic {topic}</span>
          </nav>
        </div>

        {/* Topic Header */}
        <div className="mb-8">
          <div className="flex items-start gap-4 mb-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <BookOpen className="text-blue-600 dark:text-blue-400" size={24} />
            </div>
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {topicData.title}
              </h1>
              {topicData.description && (
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                  {topicData.description}
                </p>
              )}
            </div>
          </div>

          {/* Topic Meta */}
          <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
            {topicData.estimated_time_hours && (
              <div className="flex items-center gap-2">
                <Clock size={16} />
                <span>{topicData.estimated_time_hours} hours</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                Topic {topic}
              </span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="mb-8">
          <MdxViewer source={topicData.content} />
        </div>
      </div>
    </div>
  );
};

export default Topics;
