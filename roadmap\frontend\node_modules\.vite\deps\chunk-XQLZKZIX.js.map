{"version": 3, "sources": ["../../refractor/lang/latex.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = latex\nlatex.displayName = 'latex'\nlatex.aliases = ['tex', 'context']\nfunction latex(Prism) {\n  ;(function (Prism) {\n    var funcPattern = /\\\\(?:[^a-z()[\\]]|[a-z*]+)/i\n    var insideEqu = {\n      'equation-command': {\n        pattern: funcPattern,\n        alias: 'regex'\n      }\n    }\n    Prism.languages.latex = {\n      comment: /%.*/,\n      // the verbatim environment prints whitespace to the document\n      cdata: {\n        pattern:\n          /(\\\\begin\\{((?:lstlisting|verbatim)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n        lookbehind: true\n      },\n      /*\n       * equations can be between $$ $$ or $ $ or \\( \\) or \\[ \\]\n       * (all are multiline)\n       */\n      equation: [\n        {\n          pattern:\n            /\\$\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$\\$|\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$|\\\\\\([\\s\\S]*?\\\\\\)|\\\\\\[[\\s\\S]*?\\\\\\]/,\n          inside: insideEqu,\n          alias: 'string'\n        },\n        {\n          pattern:\n            /(\\\\begin\\{((?:align|eqnarray|equation|gather|math|multline)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n          lookbehind: true,\n          inside: insideEqu,\n          alias: 'string'\n        }\n      ],\n      /*\n       * arguments which are keywords or references are highlighted\n       * as keywords\n       */\n      keyword: {\n        pattern:\n          /(\\\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n        lookbehind: true\n      },\n      url: {\n        pattern: /(\\\\url\\{)[^}]+(?=\\})/,\n        lookbehind: true\n      },\n      /*\n       * section or chapter headlines are highlighted as bold so that\n       * they stand out more\n       */\n      headline: {\n        pattern:\n          /(\\\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\\*?(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      function: {\n        pattern: funcPattern,\n        alias: 'selector'\n      },\n      punctuation: /[[\\]{}&]/\n    }\n    Prism.languages.tex = Prism.languages.latex\n    Prism.languages.context = Prism.languages.latex\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC,OAAO,SAAS;AACjC,aAAS,MAAM,OAAO;AACpB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,cAAc;AAClB,YAAI,YAAY;AAAA,UACd,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,QAAQ;AAAA,UACtB,SAAS;AAAA;AAAA,UAET,OAAO;AAAA,YACL,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA;AAAA;AAAA;AAAA;AAAA,UAKA,UAAU;AAAA,YACR;AAAA,cACE,SACE;AAAA,cACF,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,UACF;AAAA;AAAA;AAAA;AAAA;AAAA,UAKA,SAAS;AAAA,YACP,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,KAAK;AAAA,YACH,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA;AAAA;AAAA;AAAA,UAKA,UAAU;AAAA,YACR,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AACtC,QAAAA,OAAM,UAAU,UAAUA,OAAM,UAAU;AAAA,MAC5C,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}