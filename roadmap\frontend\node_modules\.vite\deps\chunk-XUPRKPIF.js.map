{"version": 3, "sources": ["../../refractor/lang/uri.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = uri\nuri.displayName = 'uri'\nuri.aliases = ['url']\nfunction uri(Prism) {\n  // https://tools.ietf.org/html/rfc3986#appendix-A\n  Prism.languages.uri = {\n    scheme: {\n      pattern: /^[a-z][a-z0-9+.-]*:/im,\n      greedy: true,\n      inside: {\n        'scheme-delimiter': /:$/\n      }\n    },\n    fragment: {\n      pattern: /#[\\w\\-.~!$&'()*+,;=%:@/?]*/,\n      inside: {\n        'fragment-delimiter': /^#/\n      }\n    },\n    query: {\n      pattern: /\\?[\\w\\-.~!$&'()*+,;=%:@/?]*/,\n      inside: {\n        'query-delimiter': {\n          pattern: /^\\?/,\n          greedy: true\n        },\n        'pair-delimiter': /[&;]/,\n        pair: {\n          pattern: /^[^=][\\s\\S]*/,\n          inside: {\n            key: /^[^=]+/,\n            value: {\n              pattern: /(^=)[\\s\\S]+/,\n              lookbehind: true\n            }\n          }\n        }\n      }\n    },\n    authority: {\n      pattern: RegExp(\n        /^\\/\\//.source + // [ userinfo \"@\" ]\n          /(?:[\\w\\-.~!$&'()*+,;=%:]*@)?/.source + // host\n          ('(?:' + // IP-literal\n            /\\[(?:[0-9a-fA-F:.]{2,48}|v[0-9a-fA-F]+\\.[\\w\\-.~!$&'()*+,;=]+)\\]/\n              .source +\n            '|' + // IPv4address or registered name\n            /[\\w\\-.~!$&'()*+,;=%]*/.source +\n            ')') + // [ \":\" port ]\n          /(?::\\d*)?/.source,\n        'm'\n      ),\n      inside: {\n        'authority-delimiter': /^\\/\\//,\n        'user-info-segment': {\n          pattern: /^[\\w\\-.~!$&'()*+,;=%:]*@/,\n          inside: {\n            'user-info-delimiter': /@$/,\n            'user-info': /^[\\w\\-.~!$&'()*+,;=%:]+/\n          }\n        },\n        'port-segment': {\n          pattern: /:\\d*$/,\n          inside: {\n            'port-delimiter': /^:/,\n            port: /^\\d+/\n          }\n        },\n        host: {\n          pattern: /[\\s\\S]+/,\n          inside: {\n            'ip-literal': {\n              pattern: /^\\[[\\s\\S]+\\]$/,\n              inside: {\n                'ip-literal-delimiter': /^\\[|\\]$/,\n                'ipv-future': /^v[\\s\\S]+/,\n                'ipv6-address': /^[\\s\\S]+/\n              }\n            },\n            'ipv4-address':\n              /^(?:(?:[03-9]\\d?|[12]\\d{0,2})\\.){3}(?:[03-9]\\d?|[12]\\d{0,2})$/\n          }\n        }\n      }\n    },\n    path: {\n      pattern: /^[\\w\\-.~!$&'()*+,;=%:@/]+/m,\n      inside: {\n        'path-separator': /\\//\n      }\n    }\n  }\n  Prism.languages.url = Prism.languages.uri\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC,KAAK;AACpB,aAAS,IAAI,OAAO;AAElB,YAAM,UAAU,MAAM;AAAA,QACpB,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,oBAAoB;AAAA,UACtB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,sBAAsB;AAAA,UACxB;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,mBAAmB;AAAA,cACjB,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA,kBAAkB;AAAA,YAClB,MAAM;AAAA,cACJ,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,KAAK;AAAA,gBACL,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,YACP,QAAQ;AAAA,YACN,+BAA+B;AAAA,aAC9B;AAAA,YACC,kEACG,SACH;AAAA,YACA,wBAAwB,SACxB;AAAA,YACF,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,uBAAuB;AAAA,YACvB,qBAAqB;AAAA,cACnB,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,uBAAuB;AAAA,gBACvB,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,kBAAkB;AAAA,gBAClB,MAAM;AAAA,cACR;AAAA,YACF;AAAA,YACA,MAAM;AAAA,cACJ,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,wBAAwB;AAAA,oBACxB,cAAc;AAAA,oBACd,gBAAgB;AAAA,kBAClB;AAAA,gBACF;AAAA,gBACA,gBACE;AAAA,cACJ;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,kBAAkB;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU,MAAM,MAAM,UAAU;AAAA,IACxC;AAAA;AAAA;", "names": []}