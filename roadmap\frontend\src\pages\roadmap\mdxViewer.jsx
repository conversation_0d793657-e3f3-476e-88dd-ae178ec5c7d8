import { useState, useEffect } from "react";
import { MD<PERSON><PERSON>rovider } from "@mdx-js/react";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";

// Code block renderer
function Code({ className = "", children, ...props }) {
  const match = /language-(\w+)/.exec(className || "");
  return match ? (
    <div className="my-4 rounded-lg overflow-hidden shadow-sm">
      <SyntaxHighlighter
        language={match[1]}
        style={oneDark}
        PreTag="div"
        {...props}
      >
        {String(children).replace(/\n$/, "")}
      </SyntaxHighlighter>
    </div>
  ) : (
    <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded my-4 overflow-x-auto" {...props}>
      <code>{children}</code>
    </pre>
  );
}

const defaultComponents = {
  code: Code,
  pre: (props) => <div {...props} />,
  h1: (props) => <h1 className="text-3xl font-bold my-4" {...props} />,
  h2: (props) => <h2 className="text-2xl font-semibold my-3" {...props} />,
  h3: (props) => <h3 className="text-xl font-medium my-2" {...props} />,
  p: (props) => <p className="leading-7 my-2" {...props} />,
  a: (props) => <a className="text-sky-600 hover:underline" {...props} />,
  ul: (props) => <ul className="list-disc ml-6 my-2" {...props} />,
  ol: (props) => <ol className="list-decimal ml-6 my-2" {...props} />,
  img: (props) => <img className="max-w-full rounded" {...props} />,
};

export default function MdxViewer({ MDXContent, srcUrl, components = {} }) {
  const [LoadedMdx, setLoadedMdx] = useState(MDXContent || null);
  const merged = { ...defaultComponents, ...components };

  useEffect(() => {
    let abortController = new AbortController();

    if (srcUrl) {
      import(/* @vite-ignore */ srcUrl)
        .then((mod) => setLoadedMdx(() => mod.default))
        .catch((err) => {
          console.error(err);
          setLoadedMdx(() => () => <div>Error loading MDX</div>);
        });
    }

    return () => abortController.abort();
  }, [srcUrl]);

  if (!LoadedMdx) {
    return <div className="p-6 text-gray-500">Loading MDX…</div>;
  }

  return (
    <article className="prose dark:prose-invert max-w-none p-6 bg-white dark:bg-gray-900 rounded-lg shadow">
      <MDXProvider components={merged}>
        <LoadedMdx />
      </MDXProvider>
    </article>
  );
}
