import { useState, useEffect, useMemo } from "react";
import { MDXProvider } from "@mdx-js/react";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";

// Code block renderer
function Code({ className = "", children, ...props }) {
  const match = /language-(\w+)/.exec(className || "");
  return match ? (
    <div className="my-4 rounded-lg overflow-hidden shadow-sm">
      <SyntaxHighlighter
        language={match[1]}
        style={oneDark}
        PreTag="div"
        {...props}
      >
        {String(children).replace(/\n$/, "")}
      </SyntaxHighlighter>
    </div>
  ) : (
    <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded my-4 overflow-x-auto" {...props}>
      <code className="text-gray-800 dark:text-gray-200">{children}</code>
    </pre>
  );
}

// Enhanced markdown parser for better content rendering
function parseMarkdown(markdown) {
  if (!markdown) return "";

  let html = markdown
    // Code blocks (must be processed before other replacements)
    .replace(/```(\w+)?\n([\s\S]*?)```/gim, (_, lang, code) => {
      return `<pre class="code-block"><code class="language-${lang || 'text'}">${code.trim()}</code></pre>`;
    })
    // Headers
    .replace(/^### (.*$)/gim, '<h3 class="text-xl font-medium my-4 text-gray-900 dark:text-white">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold my-5 text-gray-900 dark:text-white">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold my-6 text-gray-900 dark:text-white">$1</h1>')
    // Bold
    .replace(/\*\*(.*?)\*\*/gim, '<strong class="font-semibold">$1</strong>')
    // Italic
    .replace(/\*(.*?)\*/gim, '<em class="italic">$1</em>')
    // Inline code
    .replace(/`([^`]+)`/gim, '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono">$1</code>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" class="text-blue-600 dark:text-blue-400 hover:underline">$1</a>')
    // Unordered lists
    .replace(/^\* (.+$)/gim, '<li class="my-1">$1</li>')
    .replace(/(<li.*<\/li>)/gims, '<ul class="list-disc ml-6 my-4 text-gray-700 dark:text-gray-300">$1</ul>')
    // Ordered lists
    .replace(/^\d+\. (.+$)/gim, '<li class="my-1">$1</li>')
    // Blockquotes
    .replace(/^> (.+$)/gim, '<blockquote class="border-l-4 border-blue-500 pl-4 my-4 italic text-gray-600 dark:text-gray-400">$1</blockquote>')
    // Horizontal rules
    .replace(/^---$/gim, '<hr class="my-8 border-gray-300 dark:border-gray-600">')
    // Paragraphs (convert double line breaks to paragraphs)
    .replace(/\n\n/gim, '</p><p class="leading-7 my-4 text-gray-700 dark:text-gray-300">')
    // Single line breaks
    .replace(/\n/gim, '<br>');

  // Wrap in paragraph tags if not already wrapped
  if (!html.startsWith('<')) {
    html = `<p class="leading-7 my-4 text-gray-700 dark:text-gray-300">${html}</p>`;
  }

  return html;
}

const defaultComponents = {
  code: Code,
  pre: (props) => <div {...props} />,
  h1: (props) => <h1 className="text-3xl font-bold my-6 text-gray-900 dark:text-white" {...props} />,
  h2: (props) => <h2 className="text-2xl font-semibold my-5 text-gray-900 dark:text-white" {...props} />,
  h3: (props) => <h3 className="text-xl font-medium my-4 text-gray-900 dark:text-white" {...props} />,
  h4: (props) => <h4 className="text-lg font-medium my-3 text-gray-900 dark:text-white" {...props} />,
  p: (props) => <p className="leading-7 my-4 text-gray-700 dark:text-gray-300" {...props} />,
  a: (props) => <a className="text-blue-600 dark:text-blue-400 hover:underline" {...props} />,
  ul: (props) => <ul className="list-disc ml-6 my-4 text-gray-700 dark:text-gray-300" {...props} />,
  ol: (props) => <ol className="list-decimal ml-6 my-4 text-gray-700 dark:text-gray-300" {...props} />,
  li: (props) => <li className="my-1" {...props} />,
  blockquote: (props) => <blockquote className="border-l-4 border-blue-500 pl-4 my-4 italic text-gray-600 dark:text-gray-400" {...props} />,
  img: (props) => <img className="max-w-full rounded-lg shadow-md my-4" {...props} />,
  table: (props) => <table className="min-w-full border-collapse border border-gray-300 dark:border-gray-600 my-4" {...props} />,
  th: (props) => <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-800 font-semibold" {...props} />,
  td: (props) => <td className="border border-gray-300 dark:border-gray-600 px-4 py-2" {...props} />,
  hr: (props) => <hr className="my-8 border-gray-300 dark:border-gray-600" {...props} />,
};

export default function MdxViewer({ MDXContent, srcUrl, source, components = {} }) {
  const [LoadedMdx, setLoadedMdx] = useState(MDXContent || null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const merged = useMemo(() => ({ ...defaultComponents, ...components }), [components]);

  useEffect(() => {
    let abortController = new AbortController();

    const loadContent = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (srcUrl) {
          const mod = await import(/* @vite-ignore */ srcUrl);
          setLoadedMdx(() => mod.default);
        } else if (source) {
          // Create a simple component for markdown content
          const MarkdownComponent = () => {
            const parsedHtml = parseMarkdown(source);

            // Post-process to handle code blocks with syntax highlighting
            const processCodeBlocks = (html) => {
              return html.replace(
                /<pre class="code-block"><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g,
                (_, lang, code) => {
                  // Return a placeholder that will be replaced by React component
                  return `<div data-code-block data-lang="${lang}" data-code="${encodeURIComponent(code)}"></div>`;
                }
              );
            };

            return (
              <div
                dangerouslySetInnerHTML={{ __html: processCodeBlocks(parsedHtml) }}
                ref={(el) => {
                  if (el) {
                    // Replace code block placeholders with actual syntax highlighted components
                    const codeBlocks = el.querySelectorAll('[data-code-block]');
                    codeBlocks.forEach((block) => {
                      const lang = block.getAttribute('data-lang');
                      const code = decodeURIComponent(block.getAttribute('data-code'));

                      // Create a container for the syntax highlighter
                      const container = document.createElement('div');
                      container.className = 'my-4 rounded-lg overflow-hidden shadow-sm';

                      // We'll use a simple pre/code for now since SyntaxHighlighter needs React context
                      const pre = document.createElement('pre');
                      pre.className = 'bg-gray-900 p-4 overflow-x-auto';
                      const codeEl = document.createElement('code');
                      codeEl.className = `language-${lang} text-gray-100`;
                      codeEl.textContent = code;
                      pre.appendChild(codeEl);
                      container.appendChild(pre);

                      block.parentNode.replaceChild(container, block);
                    });
                  }
                }}
              />
            );
          };
          setLoadedMdx(() => MarkdownComponent);
        }
      } catch (err) {
        console.error("Error loading MDX content:", err);
        setError(err.message);
        setLoadedMdx(() => () => (
          <div className="text-red-600 dark:text-red-400 p-4 border border-red-300 dark:border-red-600 rounded-lg">
            <h3 className="font-semibold mb-2">Error loading content</h3>
            <p className="text-sm">{err.message}</p>
          </div>
        ));
      } finally {
        setIsLoading(false);
      }
    };

    if (srcUrl || source) {
      loadContent();
    }

    return () => abortController.abort();
  }, [srcUrl, source]);

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-500 dark:text-gray-400">Loading content...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center text-red-600 dark:text-red-400">
        <p>Failed to load content: {error}</p>
      </div>
    );
  }

  if (!LoadedMdx) {
    return (
      <div className="p-6 text-center text-gray-500 dark:text-gray-400">
        <p>No content to display</p>
      </div>
    );
  }

  return (
    <article className="prose dark:prose-invert max-w-none p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
      <MDXProvider components={merged}>
        <LoadedMdx />
      </MDXProvider>
    </article>
  );
}
