{"version": 3, "sources": ["../../refractor/lang/handlebars.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = handlebars\nhandlebars.displayName = 'handlebars'\nhandlebars.aliases = ['hbs']\nfunction handlebars(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.handlebars = {\n      comment: /\\{\\{![\\s\\S]*?\\}\\}/,\n      delimiter: {\n        pattern: /^\\{\\{\\{?|\\}\\}\\}?$/,\n        alias: 'punctuation'\n      },\n      string: /([\"'])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      number: /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][+-]?\\d+)?/,\n      boolean: /\\b(?:false|true)\\b/,\n      block: {\n        pattern: /^(\\s*(?:~\\s*)?)[#\\/]\\S+?(?=\\s*(?:~\\s*)?$|\\s)/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      brackets: {\n        pattern: /\\[[^\\]]+\\]/,\n        inside: {\n          punctuation: /\\[|\\]/,\n          variable: /[\\s\\S]+/\n        }\n      },\n      punctuation: /[!\"#%&':()*+,.\\/;<=>@\\[\\\\\\]^`{|}~]/,\n      variable: /[^!\"#%&'()*+,\\/;<=>@\\[\\\\\\]^`{|}~\\s]+/\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      var handlebarsPattern = /\\{\\{\\{[\\s\\S]+?\\}\\}\\}|\\{\\{[\\s\\S]+?\\}\\}/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'handlebars',\n        handlebarsPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(\n        env,\n        'handlebars'\n      )\n    })\n    Prism.languages.hbs = Prism.languages.handlebars\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC,KAAK;AAC3B,aAAS,WAAW,OAAO;AACzB,YAAM,SAAS,yBAAyB;AACvC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,aAAa;AAAA,UAC3B,SAAS;AAAA,UACT,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,YACL,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,cACb,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AACA,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,oBAAoB;AACxB,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AAAA,MACxC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}