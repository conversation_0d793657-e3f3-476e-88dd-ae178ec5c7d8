import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";


const Roadmap = () => {
  const id = useParams().id;
  const navigate = useNavigate();
  const [roadmap, setRoadmap] = React.useState(null);

  useEffect(() => {
    const fetchRoadmap = async () => {
      try {
        const response = await fetch(`http://127.0.0.1:8000/api/v1/roadmap/${id}`);
        const data = await response.json();
        if (data.success) {
          setRoadmap(data.data);
          console.log(data.data);
        }
      } catch (error) {
        console.error(error);
      }
    };
    fetchRoadmap();
  }, []);
  return (
    <div className="w-screen min-h-screen px-16 py-10 ">
      <div>
        <h1 className="text-white text-2xl text-start font-semibold">
          Roadmap
        </h1>
        <div className="grid grid-cols-3 grid-flow-row gap-4">
          {roadmap ? (
            roadmap.map((item) => (
              <div
                key={item.id} onClick={() => navigate(`/roadmap/${id}/day/${item.day}`) }
                className="max-w-[400px] min-h-56 h-full w-full border-2 border-slate-800 rounded-2xl p-4 shadow-lg bg-gradient-to-br from-slate-900 to-blue-900 flex flex-col justify-between "
              >
                <div className="mb-4 h-32 w-full bg-blue-800 rounded-xl flex items-center justify-center">
                  <span className="text-white text-lg font-bold">Day {item.day}</span>
                </div>
                <div className="flex flex-col gap-2 px-2 pb-2">
                  <h3 className="text-white text-lg font-semibold mb-1">{item.description}</h3>
                  <div className="flex gap-4 text-sm">
                    <span className="bg-blue-700 text-white px-2 py-1 rounded-full">{item.total_time_hours} hours</span>
                    <span className="bg-slate-700 text-white px-2 py-1 rounded-full">{item.topics.length} topics</span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div>Loading...</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Roadmap;
