import React from 'react';
import MdxViewer from '../pages/roadmap/mdxViewer';

const TestMdx = () => {
  const sampleMarkdown = `# Introduction to React

React is a **powerful** JavaScript library for building user interfaces.

## Key Features

- Component-based architecture
- Virtual DOM for *efficient* updates
- Declarative programming model

### Code Example

\`\`\`javascript
function Welcome(props) {
  return <h1>Hello, {props.name}!</h1>;
}

const element = <Welcome name="Sara" />;
\`\`\`

### Inline Code

You can use \`useState\` and \`useEffect\` hooks in functional components.

> React makes it painless to create interactive UIs.

## Links

Check out the [official documentation](https://reactjs.org) for more information.

---

Happy coding!`;

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">MDX Viewer Test</h1>
      <MdxViewer source={sampleMarkdown} />
    </div>
  );
};

export default TestMdx;
