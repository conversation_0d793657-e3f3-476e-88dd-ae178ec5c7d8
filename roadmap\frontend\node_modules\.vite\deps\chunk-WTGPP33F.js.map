{"version": 3, "sources": ["../../highlight.js/lib/languages/cal.js"], "sourcesContent": ["/*\nLanguage: C/AL\nAuthor: <PERSON> <<EMAIL>>\nDescription: Provides highlighting of Microsoft Dynamics NAV C/AL code files\nWebsite: https://docs.microsoft.com/en-us/dynamics-nav/programming-in-c-al\n*/\n\n/** @type LanguageFn */\nfunction cal(hljs) {\n  const KEYWORDS =\n    'div mod in and or not xor asserterror begin case do downto else end exit for if of repeat then to ' +\n    'until while with var';\n  const LITERALS = 'false true';\n  const COMMENT_MODES = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.COMMENT(\n      /\\{/,\n      /\\}/,\n      {\n        relevance: 0\n      }\n    ),\n    hljs.COMMENT(\n      /\\(\\*/,\n      /\\*\\)/,\n      {\n        relevance: 10\n      }\n    )\n  ];\n  const STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/,\n    contains: [{\n      begin: /''/\n    }]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: /(#\\d+)+/\n  };\n  const DATE = {\n    className: 'number',\n    begin: '\\\\b\\\\d+(\\\\.\\\\d+)?(DT|D|T)',\n    relevance: 0\n  };\n  const DBL_QUOTED_VARIABLE = {\n    className: 'string', // not a string technically but makes sense to be highlighted in the same style\n    begin: '\"',\n    end: '\"'\n  };\n\n  const PROCEDURE = {\n    className: 'function',\n    beginKeywords: 'procedure',\n    end: /[:;]/,\n    keywords: 'procedure|10',\n    contains: [\n      hljs.TITLE_MODE,\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING\n        ]\n      }\n    ].concat(COMMENT_MODES)\n  };\n\n  const OBJECT = {\n    className: 'class',\n    begin: 'OBJECT (Table|Form|Report|Dataport|Codeunit|XMLport|MenuSuite|Page|Query) (\\\\d+) ([^\\\\r\\\\n]+)',\n    returnBegin: true,\n    contains: [\n      hljs.TITLE_MODE,\n      PROCEDURE\n    ]\n  };\n\n  return {\n    name: 'C/AL',\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      literal: LITERALS\n    },\n    illegal: /\\/\\*/,\n    contains: [\n      STRING,\n      CHAR_STRING,\n      DATE,\n      DBL_QUOTED_VARIABLE,\n      hljs.NUMBER_MODE,\n      OBJECT,\n      PROCEDURE\n    ]\n  };\n}\n\nmodule.exports = cal;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,YAAM,WACJ;AAEF,YAAM,WAAW;AACjB,YAAM,gBAAgB;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACT,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,OAAO,aAAa;AAAA,MACxB;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}