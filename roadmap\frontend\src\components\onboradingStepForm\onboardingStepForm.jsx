import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { onboardingStepFormschema } from "@/components/onboradingStepForm/schema.jsx";
import { Form } from "@/components/ui/form";
import Step1AboutYou from "./Step1AboutYou";
import Step2Topic from "./Step2Topic";
import Step3Knowledge from "./Step3Knowledge";
import Step4Time from "./Step4Time";
import { useNavigate } from "react-router-dom";

const OnboardingStepForm = () => {
  const [currentStep, setCurrentStep] = React.useState(0);
  const navigate = useNavigate();
  const [loading, setLoading] = React.useState(false);
  const form = useForm({
    resolver: zodResolver(onboardingStepFormschema),
    mode: "onBlur",
    defaultValues: {
      currentRole: "",
      currentField: "",
      age: "",
      topic: "",
      topicReason: "",
      knowledgeLevel: 0,
      knowledgeDescription: "",
      totalDuration: "",
      daysPerWeek: 1,
      hoursPerDay: 1,
    },
  });

  const steps = ["About You", "Topic", "Knowledge", "Time"];

  const formSteps = [
    <Step1AboutYou key="step1" control={form.control} />,
    <Step2Topic key="step2" control={form.control} />,
    <Step3Knowledge key="step3" control={form.control} />,
    <Step4Time key="step4" control={form.control} />,
  ];

  const nextStep = async () => {
    setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      const response = await fetch("http://127.0.0.1:8000/api/v1/api/v1/roadmap", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      const roadmap = await response.json();
      if (roadmap.success) {
        navigate(`/roadmap/${1}`);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full h-full border-2 border-slate-800 rounded-2xl p-2 relative">
      {loading && (
        <div className="absolute top-0 left-0 w-full h-full bg-gray-500/10 rounded-2xl">
          <p>loading...</p>
        </div>
      )}

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-6 max-w-xl h-full mx-auto p-6"
        >
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <h1 className="text-white text-xl text-start font-semibold">
                {steps[currentStep]}
              </h1>
              <div>
                <span className="text-sm text-gray-400">
                  Step {currentStep + 1} of {formSteps.length}
                </span>
                {/* <div className="flex space-x-1">
                  {formSteps.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full ${
                        index <= currentStep ? "bg-gray-300" : "bg-gray-600"
                      }`}
                    />
                  ))}
                </div> */}
              </div>
            </div>
          </div>
          <div className="max-h-[250px] h-full overflow-y-auto">
            {formSteps[currentStep]}
          </div>

          <div className="flex justify-between self-end ">
            {currentStep > 0 ? (
              <button
                type="button"
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
                onClick={prevStep}
              >
                Previous
              </button>
            ) : (
              <div></div>
            )}

            {currentStep === formSteps.length - 1 ? (
              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
              >
                Submit
              </button>
            ) : (
              <button
                type="button"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
                onClick={nextStep}
              >
                Next
              </button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
};

export default OnboardingStepForm;
