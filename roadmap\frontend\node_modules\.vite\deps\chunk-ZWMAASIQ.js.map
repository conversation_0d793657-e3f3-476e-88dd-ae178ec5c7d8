{"version": 3, "sources": ["../../highlight.js/lib/languages/nix.js"], "sourcesContent": ["/*\nLanguage: Nix\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Nix functional language\nWebsite: http://nixos.org/nix\n*/\n\nfunction nix(hljs) {\n  const NIX_KEYWORDS = {\n    keyword:\n      'rec with let in inherit assert if else then',\n    literal:\n      'true false or and null',\n    built_in:\n      'import abort baseNameOf dirOf isNull builtins map removeAttrs throw ' +\n      'toString derivation'\n  };\n  const ANTIQUOTE = {\n    className: 'subst',\n    begin: /\\$\\{/,\n    end: /\\}/,\n    keywords: NIX_KEYWORDS\n  };\n  const ATTRS = {\n    begin: /[a-zA-Z0-9-_]+(\\s*=)/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: /\\S+/\n      }\n    ]\n  };\n  const STRING = {\n    className: 'string',\n    contains: [ ANTIQUOTE ],\n    variants: [\n      {\n        begin: \"''\",\n        end: \"''\"\n      },\n      {\n        begin: '\"',\n        end: '\"'\n      }\n    ]\n  };\n  const EXPRESSIONS = [\n    hljs.NUMBER_MODE,\n    hljs.HASH_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING,\n    ATTRS\n  ];\n  ANTIQUOTE.contains = EXPRESSIONS;\n  return {\n    name: 'Nix',\n    aliases: [ \"nixos\" ],\n    keywords: NIX_KEYWORDS,\n    contains: EXPRESSIONS\n  };\n}\n\nmodule.exports = nix;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,IAAI,MAAM;AACjB,YAAM,eAAe;AAAA,QACnB,SACE;AAAA,QACF,SACE;AAAA,QACF,UACE;AAAA,MAEJ;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU,CAAE,SAAU;AAAA,QACtB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,cAAc;AAAA,QAClB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AACA,gBAAU,WAAW;AACrB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,OAAQ;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}