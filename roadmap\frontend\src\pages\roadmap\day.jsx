import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeft, Clock, BookOpen, Calendar } from "lucide-react";

const Day = () => {
  const { id, day } = useParams();
  const navigate = useNavigate();
  const [roadmap, setRoadmap] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  useEffect(() => {
    const fetchRoadmap = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(
          `http://127.0.0.1:8000/api/v1/roadmap/${id}/day/${day}`
        );
        const data = await response.json();
        if (data.success) {
          setRoadmap(data.data);
          console.log(data.data);
        } else {
          setError("Failed to load day data");
        }
      } catch (err) {
        console.error(err);
        setError("Failed to fetch day data");
      } finally {
        setLoading(false);
      }
    };
    fetchRoadmap();
  }, [id, day]);
  if (loading) {
    return (
      <div className="w-screen min-h-screen px-4 md:px-16 py-10 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-300 dark:bg-gray-700 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-screen min-h-screen px-4 md:px-16 py-10 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center py-12">
            <div className="text-red-600 dark:text-red-400 text-xl mb-4">
              {error}
            </div>
            <button
              onClick={() => navigate(`/roadmap/${id}`)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Roadmap
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-screen min-h-screen px-4 md:px-16 py-10 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto">
        {/* Navigation */}
        <div className="mb-8">
          <button
            onClick={() => navigate(`/roadmap/${id}`)}
            className="flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors mb-4"
          >
            <ArrowLeft size={20} />
            Back to Roadmap
          </button>

          {/* Breadcrumb */}
          <nav className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            <span>Roadmap</span>
            <span className="mx-2">›</span>
            <span className="text-gray-900 dark:text-white">Day {day}</span>
          </nav>
        </div>

        {/* Day Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Calendar className="text-blue-600 dark:text-blue-400" size={28} />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Day {day} Topics
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                {roadmap?.length || 0} topics to explore today
              </p>
            </div>
          </div>
        </div>

        {/* Topics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {roadmap ? (
            roadmap.map((item) => (
              <div
                key={item.id}
                onClick={() => navigate(`/roadmap/${id}/day/${day}/topic/${item.topic_number}`)}
                className="group cursor-pointer bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105"
              >
                {/* Topic Icon */}
                <div className="mb-4 h-16 w-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <BookOpen className="text-white" size={24} />
                </div>

                {/* Topic Content */}
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {item.title}
                    </h3>
                    <span className="text-xs font-medium px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                      Topic {item.topic_number}
                    </span>
                  </div>

                  {item.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
                      {item.description}
                    </p>
                  )}

                  {/* Meta Info */}
                  <div className="flex items-center gap-4 pt-2">
                    {item.estimated_time_hours && (
                      <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                        <Clock size={14} />
                        <span>{item.estimated_time_hours}h</span>
                      </div>
                    )}
                    <div className="flex-1"></div>
                    <div className="text-blue-600 dark:text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity">
                      <ArrowLeft size={16} className="rotate-180" />
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <div className="text-gray-500 dark:text-gray-400">
                No topics found for this day
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Day;
