import { ThemeProvider } from "@/context/themeProvider";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import Onboarding from "./pages/onboarding/onboarding";
import Roadmap from "./pages/roadmap/roadmap";
import Day from "./pages/roadmap/day";
import Topics from "./pages/roadmap/topics";
import TestMdx from "./components/TestMdx";

function App() {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Onboarding />} />
          <Route path="/test-mdx" element={<TestMdx />} />
          <Route path="/roadmap/:id" element={<Roadmap />} />
          <Route path="/roadmap/:id/day/:day" element={<Day />} />
          <Route
            path="/roadmap/:id/day/:day/topic/:topic"
            element={<Topics />}
          />
        </Routes>
      </BrowserRouter>
    </ThemeProvider>
  );
}

export default App;
